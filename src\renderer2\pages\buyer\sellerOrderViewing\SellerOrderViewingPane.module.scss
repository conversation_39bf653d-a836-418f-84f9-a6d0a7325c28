.viewingPane {
  width: 100%;
  height: 100%;
  background-color: #1a1a1a;
  border: 1px solid #4a90e2;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
  // Subtle gradient overlay for depth
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.05) 0%, transparent 50%, rgba(74, 144, 226, 0.02) 100%);
    pointer-events: none;
  }
  
  // Optional: Add a subtle glow effect to the border
  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #4a90e2, #7b68ee, #4a90e2);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(2px);
  }
}

.placeholderContent {
  text-align: center;
  z-index: 1;
  position: relative;
  padding: 20px;
  
  // Optional: Add a subtle animation for the placeholder
  animation: fadeInUp 0.6s ease-out;
}

.placeholderText {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
  color: #b0b0b0;
  margin: 0;
  margin-bottom: 4px;
  letter-spacing: 0.2px;
  
  // Subtle text shadow for better readability
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  
  &:last-child {
    margin-bottom: 0;
  }
  
  // Optional: Add a subtle hover effect
  transition: color 0.3s ease;
  
  &:hover {
    color: #c0c0c0;
  }
}

// Animation for the placeholder content
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .viewingPane {
    border-radius: 6px;
    padding: 16px;
  }
  
  .placeholderContent {
    padding: 16px;
  }
  
  .placeholderText {
    font-size: 14px;
    line-height: 1.5;
  }
}

// Dark theme variations
@media (prefers-color-scheme: dark) {
  .viewingPane {
    background-color: #0f0f0f;
    border-color: #5a9ee2;
  }
  
  .placeholderText {
    color: #a0a0a0;
    
    &:hover {
      color: #b0b0b0;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .viewingPane {
    border-width: 2px;
    border-color: #ffffff;
  }
  
  .placeholderText {
    color: #ffffff;
    font-weight: 500;
  }
}
