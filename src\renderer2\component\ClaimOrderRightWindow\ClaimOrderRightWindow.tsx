import React from 'react'
import styles from './ClaimOrderRightWindow.module.scss'
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { format4DigitAmount, formatToTwoDecimalPlaces, getChannelWindow, priceUnits, purchaseOrder, useGlobalStore } from '@bryzos/giss-ui-library';
import { useNavigate } from 'react-router-dom';
import { routes } from 'src/renderer2/common';
import PdfMakePage from 'src/renderer2/pages/PdfMake/pdfMake';
import { descriptionLines, getOtherDescriptionLines } from 'src/renderer2/utility/pdfUtils';
import clsx from 'clsx';

const ClaimOrderRightWindow = () => {
    const navigate = useNavigate();
    const channelWindow = getChannelWindow();
    const { props } = useRightWindowStore();
    const { isRequiredSellerSettingsFilled } = useGlobalStore();
    const {
        orderDetail, availableTime, totalOrderValue, disableNextButton, disableOnclick, dialogRef, openSubmitApp,
        handleClickOpen, nextPage, setOpenReminderYouAreAlmostTherePopup, setDisableOnclick, setIsReminderPopup, handleUnDeleteClick
    } = props;


    const handleExportPDfClick = ($event) => {
        $event.stopPropagation();
        if (isRequiredSellerSettingsFilled) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setDisableOnclick(false)
        } else {
            setIsReminderPopup(false);
            setDisableOnclick(true);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }

    const calculateSellerLineWeight = (data) => {
        return formatToTwoDecimalPlaces(data.total_weight)
    }

    const getCartItems = () => {
        const { items } = orderDetail;
        const formattedItems = items.map((item, index) => ({
            description: descriptionLines(item.description),
            otherDescription: getOtherDescriptionLines(item.description),
            product_tag: item.product_tag,
            domesticMaterialOnly: item.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item.qty),
            qty_unit: item.qty_unit,
            price_unit: item.price_unit,
            price: item.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(item.seller_price_per_unit) : formatToTwoDecimalPlaces(item.seller_price_per_unit),
            line_weight: calculateSellerLineWeight(item),
            extended: formatToTwoDecimalPlaces(item.seller_line_total),
            line_weight_unit: "Lb",
            line_no: index,
            po_line: index.toString(),
            total_weight: item.total_weight
        }));
        return formattedItems
    }

    return (
        <div className={styles.claimOrderRightWindow}>
            <div className={clsx(openSubmitApp && styles.blurredContainer)}></div>
            <div className={styles.claimOrderRightWindowHeader}>
                <div className={styles.summarySection}>
                    <div className={styles.summaryRow}>
                        <div className={styles.summaryRowLbl}>Material Total</div>
                        <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(orderDetail.seller_po_price)}</div>
                    </div>
                    <div className={`${styles.summaryRow} ${styles.muted}`}>
                        <div className={styles.summaryRowLbl}>Sales Tax</div>
                        <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(orderDetail.seller_sales_tax) || 'Exempt'}</div>
                    </div>
                </div>
                <div className={clsx(styles.summaryRow, styles.totalPurchase)}>
                    <div className={styles.totalPurchaseLbl}>Total Purchase</div>
                    <div className={styles.totalPurchaseNum}>$ {formatToTwoDecimalPlaces(totalOrderValue)}</div>
                </div>

            </div>
            <div className={styles.claimOrderNote}>
                After clicking “Accept Order,” the next screen will
                be your order confirmation. Additionally, we will
                send you a purchase order for your records.
            </div>
            <div className={styles.btnSection}>
                {/* <div>
                    <button onClick={nextPage} disabled={disableNextButton}>VIEW NEXT PO</button>
                </div>
                <div>
                    <button onClick={() => navigate(routes.orderPage)}>BACK TO ALL ORDERS</button>
                </div>
                <div>
                    {(channelWindow?.fetchPdf || channelWindow?.generatePdf) && <div onClick={handleExportPDfClick}><PdfMakePage sellerData={orderDetail} disableOnclick={disableOnclick} getCartItems={getCartItems} parentContainer={dialogRef} /></div>}
                </div> */}
                <div>
                    {orderDetail.claimed_by === purchaseOrder.pending  && location.pathname === routes.previewOrderPage?
                        <button className={styles.orderPreviewBtn}>
                            ORDER PREVIEW
                            <span>AVAILABLE TO CLAIM @ {availableTime}</span>
                        </button>
                        : orderDetail.is_order_hidden === true && location.pathname === routes.deleteOrderPage ?
                        <button className={styles.orderPreviewBtn} onClick={handleUnDeleteClick}>
                            UNDELETE
                        </button>
                        :
                        <button className={styles.acceptOrderBtn} onClick={handleClickOpen}>
                        </button>
                    }
                </div>
            </div>
        </div>
    )
}

export default ClaimOrderRightWindow