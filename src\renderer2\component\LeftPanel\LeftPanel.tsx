import { routes } from 'src/renderer2/common';
import styles from './leftPanel.module.scss';
import ListTab from './ListTab/ListTab';
import RouteTab from './RouteTab/RouteTab';
import PdfNavigator from 'src/renderer2/pages/buyer/BomPdfExtractor/components/PdfNavigator';
import clsx from 'clsx';
import { useAppStore } from 'src/renderer2/utility/AppStore';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import SellerListTab from './ListTab/SellerListTab';
import { useLocation } from 'react-router-dom';

const LeftPanel = () => {
    const location = useLocation();
    const { userData } = useGlobalStore();
    const mainWindowWidth = useAppStore(state => state.mainWindowWidth);
    const screenWidth = useAppStore(state => state.screenWidth);

    return (
        <div className={clsx(styles.leftPanel, mainWindowWidth!==null && 'flexSpace', location.pathname === routes.bomExtractor && styles.pdfNavigatorMain)}>
            <div className={styles.routeTab} style={{width: `${screenWidth*0.0389}px`}}>
                <RouteTab />
            </div>
            {
                location.pathname !== routes.buyerSettingPage && location.pathname !== routes.sellerSettingPage && <>
                    {location.pathname === routes.bomExtractor ? <div className={styles.pdfNavigatorMain}>
                        <PdfNavigator />
                    </div> : <div className={styles.listTab}>
                    {
                        userData?.data?.type === 'BUYER' ? (
                            <ListTab />
                        ) : (
                            <SellerListTab />
                        )
                    }
                    </div>

                    }

                </>
            }
           
        </div>
    )
}

export default LeftPanel;