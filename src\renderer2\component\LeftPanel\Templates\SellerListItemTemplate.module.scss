.sellerListItemCard {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border: 1px solid #404040;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: #505050;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4a90e2, #7b68ee);
  }
}

.headerRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  position: relative;
}

.itemDescription {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #e0e0e0;
  line-height: 1.4;
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price {
  font-size: 16px;
  font-weight: 700;
  color: #4a90e2;
  margin-right: 8px;
  white-space: nowrap;
}

.actionIcons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.iconButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  svg {
    width: 16px;
    height: 16px;
    fill: #888;
    transition: fill 0.2s ease;
  }

  &:hover svg {
    fill: #e0e0e0;
  }

  &:first-child:hover svg {
    fill: #4a90e2;
  }

  &:last-child:hover svg {
    fill: #e74c3c;
  }
}

.detailsContainer {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detailRow {
  display: flex;
  align-items: center;
}

.location,
.weight,
.deliveryDate {
  font-size: 13px;
  color: #b0b0b0;
  line-height: 1.3;
  font-weight: 400;
}

.location {
  color: #c0c0c0;
}

.weight {
  color: #a0a0a0;
}

.deliveryDate {
  color: #d0d0d0;
  font-weight: 500;
}

// Responsive design
@media (max-width: 768px) {
  .sellerListItemCard {
    padding: 12px;
    margin: 6px 0;
  }

  .headerRow {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .itemDescription {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .price {
    align-self: flex-end;
    margin-right: 0;
  }

  .actionIcons {
    position: absolute;
    top: 12px;
    right: 12px;
  }
}

// Dark theme variations
@media (prefers-color-scheme: dark) {
  .sellerListItemCard {
    background: linear-gradient(135deg, #1e1e1e 0%, #0f0f0f 100%);
    border-color: #333;
  }
} 

.selectedOrder {
  background: white !important;
  border-color: #ddd !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
    border-color: #bbb !important;
  }

  &::before {
    background: linear-gradient(90deg, #333, #666) !important;
  }

  .itemDescription {
    color: #333 !important;
  }

  .price {
    color: #000 !important;
  }

  .location,
  .weight,
  .deliveryDate {
    color: #555 !important;
  }

  .iconButton {
    svg {
      fill: #666 !important;
    }

    &:hover svg {
      fill: #333 !important;
    }

    &:first-child:hover svg {
      fill: #4a90e2 !important;
    }

    &:last-child:hover svg {
      fill: #e74c3c !important;
    }
  }
}