.actionWindow {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  max-width: 400px;
  width: 100%;
}

.summaryCard {
  background-color: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  width: 100%;
  
  // Subtle hover effect
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: #505050;
  }
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid #3a3a3a;
  }
  
  &:first-child {
    padding-top: 0;
  }
  
  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
    margin-top: 4px;
    padding-top: 12px;
    border-top: 2px solid #4a90e2;
  }
}

.label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0;
  letter-spacing: 0.2px;
  
  // Special styling for Material Total (first row)
  .summaryRow:first-child & {
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, #4a90e2, transparent);
    }
  }
}

.value {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
  
  // Special styling for Total Sale (last row)
  .summaryRow:last-child & {
    font-size: 16px;
    font-weight: 700;
    color: #4a90e2;
  }
}

.valueSecondary {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #a0a0a0;
  letter-spacing: 0.2px;
  font-style: italic;
}

.infoBox {
  background-color: transparent;
  border: 1px solid #4a90e2;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  
  // Subtle glow effect
  box-shadow: 0 0 8px rgba(74, 144, 226, 0.2);
  
  // Optional: Add a subtle gradient background
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.05) 0%, transparent 100%);
}

.infoText {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  color: #b0b0b0;
  margin: 0;
  text-align: left;
  letter-spacing: 0.2px;
}

.acceptButton {
  background-color: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 16px 24px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  
  // Text styling
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 1px;
  
  // Shadow and depth
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
  &:hover {
    background-color: #3a3a3a;
    border-color: #505050;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
  }
}

// Responsive design
@media (max-width: 768px) {
  .actionWindow {
    padding: 16px;
    gap: 16px;
  }
  
  .summaryCard {
    padding: 12px;
  }
  
  .summaryRow {
    padding: 6px 0;
  }
  
  .label,
  .value,
  .valueSecondary {
    font-size: 13px;
  }
  
  .summaryRow:last-child .value {
    font-size: 15px;
  }
  
  .infoBox {
    padding: 12px;
  }
  
  .infoText {
    font-size: 13px;
    line-height: 1.5;
  }
  
  .acceptButton {
    padding: 14px 20px;
    font-size: 15px;
  }
}

// Dark theme variations
@media (prefers-color-scheme: dark) {
  .summaryCard {
    background-color: #1e1e1e;
    border-color: #333;
  }
  
  .summaryRow:not(:last-child) {
    border-bottom-color: #2a2a2a;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .summaryCard {
    border-width: 2px;
    border-color: #ffffff;
  }
  
  .label {
    color: #ffffff;
  }
  
  .value {
    color: #ffffff;
  }
  
  .valueSecondary {
    color: #cccccc;
  }
}
