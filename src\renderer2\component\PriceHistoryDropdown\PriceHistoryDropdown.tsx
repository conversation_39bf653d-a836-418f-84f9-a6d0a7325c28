import React, { useState, useEffect } from 'react';
import { ClickAwayListener } from '@mui/material';
import clsx from 'clsx';
import { formatToTwoDecimalPlaces } from '@bryzos/giss-ui-library';
import styles from './PriceHistoryDropdown.module.scss';

interface PriceHistoryItem {
  created_date?: string;
  qty?: string | number;
  qty_unit?: string;
  buyer_price_per_unit?: string | number;
  price_unit?: string;
  buyer_line_total?: string | number;
}

interface PriceChangeIndicator {
  show: boolean;
  direction: string;
  color: string;
  changeAmount: number;
}

interface PriceHistoryDropdownProps {
  lineHistory: string | null | undefined;
  currentPrice: string | number;
  dollerPerUmFormatter: (value: string | number) => string;
}

const PriceHistoryDropdown: React.FC<PriceHistoryDropdownProps> = ({
  lineHistory,
  currentPrice,
  dollerPerUmFormatter
}) => {
  // Internal state management
  const [showPriceHistory, setShowPriceHistory] = useState(false);
  const [priceHistoryData, setPriceHistoryData] = useState<PriceHistoryItem[]>([]);
  const [priceChangeIndicator, setPriceChangeIndicator] = useState<PriceChangeIndicator>({ 
    show: false, 
    direction: '', 
    color: '', 
    changeAmount: 0 
  });

  // Parse price history from line_history
  useEffect(() => {
    if (lineHistory) {
      try {
        const historyData = JSON.parse(lineHistory);
        if (Array.isArray(historyData)) {
          setPriceHistoryData(historyData);
          
          // Calculate price change indicator
          if (historyData.length > 0 && currentPrice) {
            // Get the original price (first item in history)
            const originalPriceItem = historyData[0];
            
            if (originalPriceItem && originalPriceItem.buyer_price_per_unit) {
              const currentPriceNum = parseFloat(String(currentPrice));
              const originalPriceNum = parseFloat(String(originalPriceItem.buyer_price_per_unit));
              
              if (!isNaN(currentPriceNum) && !isNaN(originalPriceNum) && currentPriceNum !== originalPriceNum) {
                const priceDiff = currentPriceNum - originalPriceNum;
                setPriceChangeIndicator({
                  show: true,
                  direction: priceDiff > 0 ? '↑' : '↓',
                  color: priceDiff > 0 ? '#ff6b6b' : '#51cf66',
                  changeAmount: Math.abs(priceDiff)
                });
              } else {
                setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
              }
            } else {
              setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
            }
          } else {
            setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
          }
        }
      } catch (error) {
        console.error('Error parsing line_history:', error);
        setPriceHistoryData([]);
        setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
      }
    } else {
      setPriceHistoryData([]);
      setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
    }
  }, [lineHistory, currentPrice]);
  return (
    <div 
      className={styles.pricePerUnitContainer}
      onMouseEnter={() => {
        if (priceHistoryData.length > 0) {
          setShowPriceHistory(true);
        }
      }}
      onMouseLeave={() => {
        setShowPriceHistory(false);
      }}
      onClick={() => {
        if (priceHistoryData.length > 0) {
          setShowPriceHistory(!showPriceHistory);
        }
      }}
    >
      {/* Price Change Indicator */}
      {priceChangeIndicator.show && (
        <span 
          className={styles.priceChangeArrow}
          style={{ color: priceChangeIndicator.color }}
        >
          {priceChangeIndicator.direction}
        </span>
      )}
      
      {!!currentPrice && dollerPerUmFormatter(currentPrice)}
      
      {/* Price History Dropdown */}
      {showPriceHistory && priceHistoryData.length > 0 && (
        <ClickAwayListener onClickAway={() => setShowPriceHistory(false)}>
          <div className={styles.priceHistoryDropdown}>
            {priceHistoryData.map((historyItem, historyIndex) => (
              <div key={historyIndex} className={styles.priceHistoryRow}>
                <div className={styles.priceHistoryLabel}>
                  {historyItem.created_date || 'Original'}
                </div>
                <div className={styles.priceHistoryValues}>
                  <span className={styles.priceHistoryQty}>
                    {String(historyItem.qty || '')}
                  </span>
                  <span className={styles.priceHistoryUnit}>
                    {String(historyItem.qty_unit || '')}
                  </span>
                  <span className={clsx(
                    styles.priceHistoryPrice,
                    String(historyItem.buyer_price_per_unit) === formatToTwoDecimalPlaces(String(currentPrice)) && styles.currentPrice
                  )}>
                    {formatToTwoDecimalPlaces(String(historyItem.buyer_price_per_unit || 0))}
                  </span>
                  <span className={styles.priceHistoryUnit}>
                    {String(historyItem.price_unit || '')}
                  </span>
                  <span className={styles.priceHistoryTotal}>
                    {formatToTwoDecimalPlaces(String(historyItem.buyer_line_total || 0))}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </ClickAwayListener>
      )}
    </div>
  );
};

export default PriceHistoryDropdown; 