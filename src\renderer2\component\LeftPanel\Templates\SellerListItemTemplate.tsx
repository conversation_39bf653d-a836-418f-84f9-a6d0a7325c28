import { getChannelWindow, priceUnits, useGlobalStore, usePostSellerHideOrder, useSellerOrderStore } from "@bryzos/giss-ui-library";
import moment from "moment";
import { format4DigitAmount, formatToTwoDecimalPlaces } from "src/renderer2/helper";
import { ReactComponent as DeleteIcon } from "../../../assets/New-images/New-Image-latest/delete-outlined.svg";
import { ReactComponent as ShareIcon } from "../../../assets/New-images/New-Image-latest/share-outlined.svg";
import styles from "./SellerListItemTemplate.module.scss";
import { purchaseOrder, referenceDataKeys } from "src/renderer2/common";
import { clsx } from "clsx";
import { descriptionLines, getOtherDescriptionLines } from "src/renderer2/utility/pdfUtils";
import { useRef } from "react";
import PdfMakePage from "src/renderer2/pages/PdfMake/pdfMake";

const SellerListItemTemplate = ({ order, handleClickOrder, index , label , disbaleExportPoBtn, handleExportPDfClick }: any) => {
  const { referenceData } = useGlobalStore();
  const stateRef = referenceData?.ref_states;
  const state = stateRef?.find(stateDetail => stateDetail.id == order.state_id)?.code;
  const deliveryDate = moment.utc(order.delivery_date).tz('America/Chicago').format('M/DD/YY');
  const distinctHeaderSet = new Set();
  const availableAfter = referenceData?.ref_general_settings.find(setting => setting.name === referenceDataKeys.sellerAvailInMinKey).value;
  const domesticMaterialText = referenceData?.ref_general_settings.find(setting => setting.name === referenceDataKeys.domesticMaterialTextKey).value;
  const createdDate = order.payment_method === purchaseOrder.paymentMethodBNPL ? order.created_date : order.ach_po_approved_date;
  const availableTime = moment.utc(createdDate).add((+availableAfter) + 1, 'minute').local().format('h:mm a');
  const orderDetail = useSellerOrderStore(state => state.orderToBeShownInOrderAccept);
  order.items.forEach((item) => distinctHeaderSet.add(item.shape));
  let poHeader = '';
  let loop = 0;
  distinctHeaderSet.forEach((item) => {
    poHeader += item;
    if (loop < distinctHeaderSet.size - 1) poHeader += ', ';
    loop++;
  })
  const materialValue = +order.seller_po_price;
  const salesTaxValue = +order.seller_sales_tax;
  const totalOrderValue = (materialValue + salesTaxValue).toFixed(2);
  const {mutateAsync:postSellerHideOrder} = usePostSellerHideOrder()
  const ref = useRef(null);
  const channelWindow = getChannelWindow();


  const handleShareClick = (e: React.MouseEvent, item: any) => {
    e.stopPropagation();
  }
  const handleDeleteClick = async (e: React.MouseEvent, item: any) => {
    e.stopPropagation();
    const payload = {
      data: {
        user_purchase_order_id: order.id,
        hide: true
      }
    }
    try {
      console.log("payload", payload)
      await postSellerHideOrder(payload)
    } catch (error) {
      console.log(error);
    }
  }
  
  const getCartItems = () => {
    const { items } = orderDetail;
    const formattedItems = items.map((item, index) => ({
        description: descriptionLines(item.description),
        otherDescription: getOtherDescriptionLines(item.description),
        product_tag: item.product_tag,
        domesticMaterialOnly: item.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
        qty: formatToTwoDecimalPlaces(item.qty),
        qty_unit: item.qty_unit,
        price_unit: item.price_unit,
        price: item.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(item.seller_price_per_unit) : formatToTwoDecimalPlaces(item.seller_price_per_unit),
        line_weight: formatToTwoDecimalPlaces(item.total_weight),
        extended: formatToTwoDecimalPlaces(item.seller_line_total),
        line_weight_unit: "Lb",
        line_no: index,
        po_line: index.toString(),
        total_weight: item.total_weight
    }));
    return formattedItems
}
  return (
    <div className={clsx(styles.sellerListItemCard , orderDetail.id === order.id && styles.selectedOrder)} aria-disabled={orderDetail.id === order.id}  onClick={(e) => {
      // Disable onClick when the order is already selected
      if (orderDetail.id === order.id) {
        return;
      }
      handleClickOrder(e, order, index, false , label === 'Filtered');
    }}>
      <div className={styles.headerRow}>
        <div className={styles.itemDescription}>
          {poHeader.length > 20 ? `${poHeader.substring(0, 20)}...` : poHeader}
        </div>
        <div className={styles.price}>
          ${formatToTwoDecimalPlaces(totalOrderValue)}
        </div>
        <div className={styles.actionIcons}>
          <button 
            className={styles.iconButton} 
            title="Share"
            disabled={disbaleExportPoBtn}
            onClick={handleExportPDfClick}
          >
            <ShareIcon />
          </button>
          <button 
            className={styles.iconButton} 
            onClick={(e) => handleDeleteClick(e, order)}
            title="Delete"
          >
            <DeleteIcon />
          </button>
                              {/* {(channelWindow?.fetchPdf || channelWindow?.generatePdf) && <div onClick={handleExportPDfClick}><PdfMakePage sellerData={orderDetail} disableOnclick={disbaleExportPoBtn} getCartItems={getCartItems} parentContainer={ref} /></div>} */}

        </div>
      </div>
      
      <div className={styles.detailsContainer}>
        <div className={styles.detailRow}>
          <span className={styles.location}>
            Buyer PoNumber: {order.buyer_po_number}
          </span>
        </div>
      <div className={styles.detailRow}>
          <span className={styles.location}>
            Active In: {availableTime}
          </span>
        </div>
        <div className={styles.detailRow}>
          <span className={styles.location}>
            {order.city}, {state}
          </span>
        </div>
        <div className={styles.detailRow}>
          <span className={styles.weight}>
            {formatToTwoDecimalPlaces(order.total_weight)} LBS
          </span>
        </div>
        <div className={styles.detailRow}>
          <span className={styles.deliveryDate}>
            Deliver By: {deliveryDate}
          </span>
        </div>
      </div>
    </div>
  )
}

export default SellerListItemTemplate
