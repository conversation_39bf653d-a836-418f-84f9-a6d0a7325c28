import React from 'react'
import styles from './RouteTab.module.scss';
import { useNavigate } from 'react-router-dom';
import { routes, sellerView, sellerViewConstants } from 'src/renderer2/common';
import { useAuthStore, useGlobalStore, useSellerOrderStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import { ReactComponent as InstantPriceIcon } from '../../../assets/New-images/New-Image-latest/Instant Price Search.svg';
import { ReactComponent as InstantPriceIconHover } from '../../../assets/New-images/New-Image-latest/Instant Price Search Active.svg';

const RouteTab = () => {
  const navigate = useNavigate();
  const {initiateLogout} = useAuthStore();
  const {userData} = useGlobalStore();
  
  const handleLogout = () => {
    // setCloseWithoutAnimation(true);
    // setOpenLeftPanel(false);
    initiateLogout(false, false, true);
}

  return (
    <div className={styles.navTab}>
        <div className={styles.routingPanel}>
          {
            userData?.data?.type === 'BUYER'  ? (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing)} onClick={() => navigate(routes.homePage)}><div className={styles.routeOptions}><span className={styles.instantPriceIcon}><InstantPriceIcon className={styles.instantPriceIcon1} /><InstantPriceIconHover className={styles.instantPriceIcon2} /></span><span className={clsx(styles.optionHoverBg, styles.pricingHover)}></span><span className={clsx(styles.routeBtnHover, styles.pricingHover)}>INSTANT PURCHASING</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.quoting)} onClick={() => navigate(routes.savedBom)}><div className={styles.routeOptions}><span>Q</span><span className={clsx(styles.optionHoverBg, styles.quotingHover)}></span><span className={clsx(styles.routeBtnHover, styles.quotingHover)}>UOTING</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.purchasing)} onClick={() => navigate(routes.createPoPage)}><div className={styles.routeOptions}><span>P</span><span className={clsx(styles.optionHoverBg, styles.purchasingHover)}></span><span className={clsx(styles.routeBtnHover, styles.purchasingHover)}>URCHASING</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.order)} onClick={() => navigate(routes.orderManagementPage)}><div className={styles.routeOptions}><span>O</span><span className={clsx(styles.optionHoverBg, styles.orderHover)}></span><span className={clsx(styles.routeBtnHover, styles.orderHover)}>RDER MANAGEMENT</span></div></button>
              </>
            ) : (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing)} onClick={() => {navigate(routes.previewOrderPage);}}><div className={styles.routeOptions}><span>P</span><span className={clsx(styles.optionHoverBg, styles.pricingHover)}></span><span className={clsx(styles.routeBtnHover, styles.pricingHover)}>REVIEW</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.quoting)} onClick={() => {navigate(routes.orderPage);}}><div className={styles.routeOptions}><span>C</span><span className={clsx(styles.optionHoverBg, styles.quotingHover)}></span><span className={clsx(styles.routeBtnHover, styles.quotingHover)}>LAIM</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.order)} onClick={() => {navigate(routes.orderManagementPage);}}><div className={styles.routeOptions}><span>O</span><span className={clsx(styles.optionHoverBg, styles.orderHover)}></span><span className={clsx(styles.routeBtnHover, styles.orderHover)}>RDER MANAGEMENT</span></div></button>
                <button className={clsx(styles.sideBarButton, styles.purchasing)} onClick={() => {navigate(routes.deleteOrderPage)}}><div className={styles.routeOptions}><span>D</span><span className={clsx(styles.optionHoverBg, styles.purchasingHover)}></span><span className={clsx(styles.routeBtnHover, styles.purchasingHover)}>ELETE</span></div></button>
              </>
            )
          }
        </div>
        <div className={styles.logout}>
            <button className={styles.logoutButton} onClick={handleLogout}>Logout</button>
        </div>  
    </div>
  )
}

export default RouteTab