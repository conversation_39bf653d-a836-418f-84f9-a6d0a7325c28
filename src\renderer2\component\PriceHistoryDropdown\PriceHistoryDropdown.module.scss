.pricePerUnitContainer {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &:hover {
    .priceHistoryDropdown {
      display: block;
    }
  }
  
  .priceHistoryIndicator {
    font-size: 8px;
    color: rgba(255, 255, 255, 0.6);
    margin-left: 2px;
  }
  
  .priceChangeArrow {
    font-size: 10px;
    font-weight: 600;
    margin-right: 4px;
    display: inline-block;
    cursor: pointer;
  }
}

.priceHistoryDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: rgba(35, 35, 35, 0.98);
  border-radius: 8px;
  padding: 16px;
  min-width: 320px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  display: none;
  margin-top: 4px;
  animation: fadeIn 0.2s ease-in-out;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .priceHistoryRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
    
    .priceHistoryLabel {
      font-size: 13px;
      color: #aaa;
      font-weight: 500;
      min-width: 70px;
      text-align: left;
    }
    
    .priceHistoryValues {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 13px;
      color: #aaa;
      
      .priceHistoryQty,
      .priceHistoryUnit,
      .priceHistoryTotal {
        color: #aaa;
        font-weight: 400;
      }
      
      .priceHistoryPrice {
        color: #aaa;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
        
        &.currentPrice {
          color: #ff8c00;
          font-weight: 600;
        }
        
        .currentPriceArrow {
          font-size: 10px;
          color: #ff8c00;
          font-weight: 600;
        }
      }
    }
  }
} 