.listTab{
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    border-style: solid;
    background-color: #191a20;
    .title{
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: -0.64px;
        text-align: left;
        text-transform: uppercase;
    }
    .quoting{
        color: #32ccff;
    }
    .purchasing{
        color: #ffe352;
    }
    .instantPriceSearch{
        color: #32ff6c;
    }
    .order{
        color: #ff8c4c;
    }
    .filterSection{
        width: 100%;
        display: flex;
        gap: 8px;
        padding: 16px;
        .filterSectionLeft{
            width: 48.39%;
            height: 36px;
            flex-grow: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.04);
        }
        .filterSectionRight{
            width: 51.61%;
            height: 36px;
            flex-grow: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.04);
        }
    }
    .listSection{
        flex: 1;
        padding-left: 16px;
        padding-right: 5px;
        max-height: 83.13%;
        .savedSearchListContainer {
            height: 100%;
            overflow: auto;
            padding-right: 5px;
            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
        
            &::-webkit-scrollbar-thumb {
                background:#9b9eac;
                border-radius: 50px;
            }
        
            &::-webkit-scrollbar-track {
                background: transparent;
            }
        }
    }
    .createNew{
       width: 100%;
       padding: 16px;
    }
    .titleSection{
        width: 100%;
        padding: 0px 16px;
    }
}
.dropDownBG.dropDownBG {
width: 110px;
z-index: 999;
padding: 4px;
border-radius: 8px;
-webkit-backdrop-filter: blur(20px);
backdrop-filter: blur(20px);
background-color: #9c9da5;
margin-top: 10px;

    ul {
        padding: 0px;

        li {
        font-family: Inter;
        font-size: 14px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: center;
        color: #191a20;
        margin-bottom: 2px;
        &[aria-selected="true"] {
            border-radius: 6px;
            background-color: #e0e0e0;
        }
        &:hover {
            border-radius: 6px;
            background-color: #e0e0e0;
        }
        }
    }
}
.searchContainer {
    width: 100%;
    margin-bottom: 16px;
    
    &:first-child {
        .searchLabel {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
    }
    
    .searchLabel {
        background-color: #303136;
        height: 40px;
        display: flex;
        align-items: center;
        padding-left: 12px;
        font-family: Syncopate;
        font-size: 12px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 0.84px;
        text-align: left;
        color: #fff;
        text-transform: uppercase;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 1px solid #404040;
    }
    
    .searchItemsContainer {
        max-height: 400px;
        overflow-y: auto;
        background-color: #222329;
        
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
    
        &::-webkit-scrollbar-thumb {
            background: #9b9eac;
            border-radius: 50px;
        }
    
        &::-webkit-scrollbar-track {
            background: transparent;
        }
    }
    
    .searchItemContainer {
        background-color: #222329;
        padding: 8px 12px;
        border-bottom: 1px solid #2a2a2a;
        
        &:last-child {
            border-bottom: none;
        }
        
        .searchTitle {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #fff;
            display: flex;
            position: relative;
            padding-bottom: 17px;
            .searchTitleText {
                display: flex;
                align-items: center;
                gap: 4px;
            }
            .itemCount {
                margin-left: auto;
            }
            .iconContainer {
                position: absolute;
                right: 0;
                top: 28px;
                display: flex;
                gap: 8px;
                .shareIcon {
                    cursor: pointer;
                }
                .deleteIcon {
                    cursor: pointer;
                }
            }
        }
        .searchDetails {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.84px;
            text-align: left;
            color: #9b9eac;
            padding-bottom: 17px;
        }
    }
    .selectedSearchItem {
        background-color: #434449;
    }
}
    
.noDataContainer {
    background-color: #222329;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    
    .noDataMessage {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.98px;
        text-align: center;
        color: #9b9eac;
    }
}