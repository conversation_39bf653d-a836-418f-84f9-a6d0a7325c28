import React from 'react'
import styles from './SellerOrderViewingActionWindow.module.scss';
import { routes } from 'src/renderer2/common';
import { useLocation } from 'react-router-dom';

const SellerOrderViewingActionWindow = () => {
  const location = useLocation();

  const handleAcceptOrder = () => {
    // Handle accept order action
    console.log('Accept Order clicked');
  };

  return (
    <div className={styles.actionWindow}>
      <div className={styles.summaryCard}>
        <div className={styles.summaryRow}>
          <span className={styles.label}>Material Total</span>
          <span className={styles.value}>$ 0.00</span>
        </div>
        <div className={styles.summaryRow}>
          <span className={styles.label}>Sales Tax</span>
          <span className={styles.valueSecondary}>Exempt</span>
        </div>
        <div className={styles.summaryRow}>
          <span className={styles.label}>Total Sale</span>
          <span className={styles.value}>$ 0.00</span>
        </div>
      </div>
      
      <div className={styles.infoBox}>
        <p className={styles.infoText}>
          After clicking "Accept Order," the next screen will be your order confirmation. Additionally, we will send you a purchase order for your records.
        </p>
      </div>
      {
        (location.pathname === routes.orderPage || location.pathname === routes.deleteOrderPage ) &&  (
          <button className={styles.acceptButton} >
            {location.pathname === routes.orderPage ? 'ACCEPT ORDER' : 'DELETE ORDER'}
          </button>
        )
      }
    </div>
  )
}

export default SellerOrderViewingActionWindow
