import React, { useEffect } from 'react'
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import styles from './SellerOrderViewingPane.module.scss';
import SellerOrderViewingActionWindow from './SellerOrderViewingActionWindow';
import { useSellerOrderStore } from '@bryzos/giss-ui-library';
import AcceptOrder from '../../seller/acceptOrder';

const SellerOrderViewingPane = () => {
  const { setLoadComponent, setProps } = useRightWindowStore();
  const { orderToBeShownInOrderAccept } = useSellerOrderStore();

  useEffect(() => {
    setLoadComponent(<SellerOrderViewingActionWindow />);
    return () => {
      setLoadComponent(null);
    }
  }, [location.pathname]);

  useEffect(() => {
    console.log("orderToBeShownInOrderAccept", orderToBeShownInOrderAccept)
  }, [orderToBeShownInOrderAccept])

  return (
    <>
      {
        Object.keys(orderToBeShownInOrderAccept).length > 0 ? (
          <div>
            <AcceptOrder key={orderToBeShownInOrderAccept.id}/>
          </div>
        ) : (
          <div className={styles.viewingPane}>
            <div className={styles.placeholderContent}>
              <p className={styles.placeholderText}>
                This is the viewing pane.
              </p>
              <p className={styles.placeholderText}>
                Select from the list on the left to
              </p>
              <p className={styles.placeholderText}>
                review an order.
              </p>
            </div>
          </div>

        )
      }
    </>
  )
}

export default SellerOrderViewingPane
