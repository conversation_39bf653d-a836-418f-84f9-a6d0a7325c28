// @ts-nocheck
import {  routes } from '../../common';
import { Auth } from 'aws-amplify';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from "yup";
import { useHeightListener } from '../../hooks/useHeightListener';
import { useEffect, useState } from 'react';
import { changePasswordConst, commomKeys, decryptData, encryptData, useGlobalStore, usePostMigrateToPassword, getChannelWindow, useGlobalSignoutApi, channelWindowList } from '@bryzos/giss-ui-library';
import { useNavigate } from 'react-router';
import useDialogStore from '../DialogPopup/DialogStore';
import styles from './changePassword.module.scss'
import { clsx } from 'clsx';
import { ReactComponent as ErrorEmailIcon } from '../../assets/images/errorEmail.svg';
import { ReactComponent as ShowPassIcon } from '../../assets/images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../assets/images/hide-pass.svg';
import { Fade, Tooltip } from '@mui/material';
import InputWrapper from '../InputWrapper';
import CustomPasswordField from '../CustomPasswordField';

const ChangePassword = ({closeDialog, deviceId}) => {
    const channelWindow = getChannelWindow();
    const navigate = useNavigate();
    const isChangePasswordModule = (location.pathname === routes.changePassword);
    const { decryptionEntity, userData, setGlobalForceLogout, setAutoLogin, isManualLogin } = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const changePasswordConfirmation = usePostMigrateToPassword();
    const [showOldPassword, setShowOldPassword] = useState(true);
    const [passwordVisibility, setPasswordVisibility] = useState({
        password1: true,
        password2: true,
        password3: true,
    });
    const globalSignout = useGlobalSignoutApi();
    const [isInputFocused, setIsInputFocused] = useState({
        userType: false,
        CompanyName: false,
        CompanyEntity: false,
        ZipCode: false,
        FirstName: false,
        LastName: false,
        EmailAddress: false,
        ReEnterEmailAddress: false,
        submitBtn: false,
      });

    const togglePasswordVisibility = (field) => {
        setPasswordVisibility((prevState) => ({
            ...prevState,
            [field]: !prevState[field],
        }));
    };
    const { register, handleSubmit, setValue, getValues, setError, clearErrors, watch, formState: { errors, isValid } } = useForm({
        resolver: yupResolver(
            yup.object({
                currentPassword: yup.string().test('len', 'Password must be 6 digits', val => val?.length >= 6),
                password: yup.string().trim()
                .required('Password is required')
                .min(8, 'Password must be at least 8 characters')
                .matches(/[A-Z]/, 'Password must contain at least 1 capital letter')
                .matches(/[0-9!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least 1 number or symbol'),
              confirmPassword: yup.string().test("isRequired", "Password does not match!", function (value:string | undefined ) {
                const password = this.parent.password;
                if (password.trim() === value?.trim()) return true;
                return false;
             }),
            }).required()
        ),
        mode: 'onSubmit',
    });

    useEffect(() => {
        (async () => {
            if (channelWindow?.getLoginCredential && decryptionEntity) {
                const cred = window.electron.sendSync({ channel: channelWindow.getLoginCredential });
                if (cred && isChangePasswordModule && isManualLogin ) {
                    const data = JSON.parse(await decryptData(cred, decryptionEntity.decryption_key, true));
                    setValue('currentPassword', data.password);
                    setShowOldPassword(false);
                }
            }
        })()
    }, [])

    async function submit({ password, currentPassword }) {
        try {
            if (userData) {
                const user = await Auth.currentAuthenticatedUser();
                await Auth.changePassword(user, currentPassword, password);
                const email = userData.data?.email_id;
                if (channelWindow?.setLoginCredential && email && password && decryptionEntity && isChangePasswordModule) {
                    const _encryptCredential = await encryptData(JSON.stringify({ email, password: password }), decryptionEntity.decryption_key);
                    window.electron.send({ channel: channelWindow.setLoginCredential, data: _encryptCredential });
                }
                if(isChangePasswordModule)
                changePasswordConfirmation.mutateAsync(email).then(async () => {
                    onChangePassword(email);
                })
                else
                onChangePassword(email);
            }
        } catch (error) {
            if (error.message === changePasswordConst.changePasswordCognitoError) error.message = changePasswordConst.incorrectOldPasswordError;
            showCommonDialog(null, error.message ?? changePasswordConst.onError, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
    }

    const onChangePassword = async (email_id) => {
        try{
            await globalSignout.mutateAsync({data:{email_id,device_id: deviceId}});
        }catch(err){
            console.error(err)
        }
        finally{
            if(closeDialog) closeDialog();
            showCommonDialog(null, changePasswordConst.onSuccess, null, resetDialog, [{ name: commomKeys.continue, action: resetDialog }]);
        }
    }

    const resetDialog = () => {
        resetDialogStore();
        if(isChangePasswordModule) setAutoLogin(true);
        else setGlobalForceLogout(routes.loginPage);
    }

    const handleForgotPassword = () => {
        showCommonDialog(commomKeys.warning, changePasswordConst.forgotPasswordPrompt, null, resetDialog, [{ name: commomKeys.yes, action: naviagateToForgotPassword },{ name: commomKeys.no, action: resetDialogStore }]);
    }

    const naviagateToForgotPassword = () => {
        resetDialogStore();
        setGlobalForceLogout(routes.forgotPassword);
    }

    const handlePasswordBlur = () => {
        const password = getValues('password')?.trim();
        const confirmPassword = getValues('confirmPassword')?.trim();
        if(password.length && confirmPassword?.length){
            if (password === confirmPassword) {
              clearErrors(["password", "confirmPassword"]);
            } else{ 
                setError("password", { message: "Password does not match!" });
            }
        }
      }

      
  const handleInputFocus = (inputName) => {
    setIsInputFocused((inputState) => ({
      ...inputState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName) => {
    setIsInputFocused((inputState) => ({
      ...inputState,
      [inputName]: false,
    }));
  };
   
    return (
        <div className={clsx(styles.resetPasscontainer, (!isChangePasswordModule && styles.changePassPopup))}>
            <div className={styles.bryzosTitle}>BRYZOS</div>
            <p className={styles.resetPassTitle}>
                {(!isChangePasswordModule) ?  'Change your password' : 'Set Your New Password'}
            </p>
            {/* {isChangePasswordModule &&
                <p className={styles.noteText}>
                    {userData?.data?.is_migrated_to_password === 0 ?
                        "As part of our recent system improvements, we’re asking you to set a new password for enhanced security. Please take a moment to update your credentials below."
                        :
                        "We need you to reset your password since it was recently reset by an admin. Please take a moment to update your credentials below for continued access."
                    }
                </p>
            } */}
            <div className={styles.changePassInnerContent}>
                {showOldPassword && 
                <div className={styles.formGroupInput}>
                <span className={styles.col1}>

                    <div className={clsx(styles.confirmPasswordInput, (isInputFocused.currentPassword) && styles.focusPass, (isInputFocused.confirmPassword) && styles.bgRemove)}>

                        <InputWrapper>
                            <CustomPasswordField
                                onChange={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register('currentPassword').onChange(e);
                                }}
                                placeholder={'Enter your current password'}
                                classNameWrapper={styles.passwordInput}
                                isConfirmPassword={true}
                                register={register("currentPassword")}
                                onFocus={() => handleInputFocus('currentPassword')}
                                onBlur={(e) => {
                                    register("currentPassword").onBlur(e);
                                    handleInputBlur('currentPassword')
                                    handlePasswordBlur()
                                }}
                                showPassAtInitial={true}
                            />
                        </InputWrapper>
                        <div className={styles.forgotPasswordTxt}><span onClick={handleForgotPassword}>Forgot Password?</span></div>
                    </div>
                </span>
            </div>
                }

                <div className={styles.passwordErrorContainer}>
                <div className={styles.formGroupInput}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.password && styles.focusLbl)} htmlFor="password">CREATE PASSWORD</label>
              </span>
              <span className={styles.col1}>

                <div className={clsx(styles.confirmPasswordInput, (isInputFocused.password) && styles.focusPass, (isInputFocused.confirmPassword) && styles.bgRemove)}>

                  <InputWrapper>
                    <CustomPasswordField
                      onChange={(e) => {
                        e.target.value = e.target.value.trim();
                        register('password').onChange(e);
                      }}
                      placeholder={''}
                      classNameWrapper={styles.passwordInput}
                      isConfirmPassword={true}
                      register={register("password")}
                      currentText={watch('password')}
                      targetText={watch('confirmPassword') ?? ''}
                      onFocus={() => handleInputFocus('password')}
                      onBlur={(e) => {
                        register("password").onBlur(e);
                        handleInputBlur('password')
                        handlePasswordBlur()
                      }}
                      tabIndex={8}
                    />
                  </InputWrapper>
                </div>
              </span>
            </div>

            <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
              <span className={styles.col1}>
                <label className={clsx(isInputFocused.confirmPassword && styles.focusLbl)} htmlFor="confirmPassword">RE-ENTER PASSWORD</label>
              </span>
              <span className={styles.col1}>

                <div className={clsx(styles.confirmPasswordInput, (isInputFocused.confirmPassword) && clsx(styles.focusPass, styles.focusPass2), (isInputFocused.password) && styles.bgRemove)}>
                  <InputWrapper>
                    <CustomPasswordField
                      onChange={(e) => {
                        e.target.value = e.target.value.trim();
                        register('confirmPassword').onChange(e);
                      }}
                      placeholder={''}
                      classNameWrapper={clsx(styles.passwordInput, styles.passwordInput2)}
                      isConfirmPassword={true}
                      register={register("confirmPassword")}
                      currentText={watch('confirmPassword')}
                      targetText={watch('password') ?? ''}
                      onBlur={(e) => {
                        register("confirmPassword").onBlur(e);
                        handleInputBlur('confirmPassword')
                        handlePasswordBlur()
                      }}
                      onFocus={() => handleInputFocus('confirmPassword')}
                      tabIndex={9}
                    />
                  </InputWrapper>
                  {(isInputFocused.password || isInputFocused.confirmPassword) &&
                    <div className={clsx(styles.passwordRequirements, (isInputFocused.confirmPassword) && styles.passwordRequirements2)}>
                      <span className={clsx(styles.passwordRequirementItem, watch('password')?.length >= 8 && styles.passwordRequirementItemActive)}> 8 Characters Min </span>
                      <span className={clsx(styles.passwordRequirementItem, /[A-Z]/.test(watch('password') || '') && styles.passwordRequirementItemActive)}> 1 Uppercase </span>
                      <span className={clsx(styles.passwordRequirementItem, /[0-9!@#$%^&*(),.?":{}|<>]/.test(watch('password') || '') && styles.passwordRequirementItemActive)}> 1 non-Letter </span>
                    </div>
                  }
                </div>
              </span>

            </div>

                </div>

            </div>
            <div className={styles.btnSection}>
                <button className={clsx(styles.btnReset,styles.saveBtnChangePass)} onClick={() => { handleSubmit(submit)() }} disabled={!isValid || Object.keys(errors).length > 0 }>Save Password</button>
            </div>
        </div>
    );
};

export default ChangePassword;